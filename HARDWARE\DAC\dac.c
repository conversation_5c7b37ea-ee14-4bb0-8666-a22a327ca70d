#include "dac.h"
#include <stdio.h>

// 定义PI常数（如果编译器没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 频率-幅度映射表 (100Hz-3000Hz，每100Hz一个点，超过3000Hz使用固定幅度)
static const float amplitude_table[DAC_FREQ_TABLE_SIZE] = {
    4.615, 4.462, 4.325, 4.058, 3.727,
    3.448, 3.175, 2.924, 2.686, 2.475,
    2.278, 2.117, 1.977, 1.876, 1.764,
    1.670, 1.574, 1.482, 1.398, 1.324,
    1.255, 1.192, 1.133, 1.073, 1.024,
    0.974, 0.9266, 0.8827, 0.8415, 0.825
};

// 正弦波查找表
static uint16_t sine_table[DAC_SINE_SAMPLES];

// DMA缓冲区 - 用于循环输出正弦波数据
static uint16_t dma_buffer[DAC_SINE_SAMPLES];

// 当前使用的DMA缓冲区大小
static uint32_t current_dma_buffer_size = DAC_SINE_SAMPLES;

// 静态函数前向声明
static void DAC_GenerateSineTable(float amplitude);
static void DAC_UpdateDMABuffer(void);
static void DAC_UpdateTimerFrequency(float sample_rate);

// 正弦波输出控制变量
float dac_output_frequency = 100.0f;    // 当前输出频率
uint8_t dac_output_enabled = 0;         // 输出使能标志（默认关闭）
uint8_t dac_user_enabled = 0;           // 用户使能标志（按钮控制）
float dac_amplitude_multiplier = 1.0f;  // 幅度倍数 (1.0, 1.1, 1.2, ..., 2.0)
static float current_amplitude = 0.5f;  // 当前幅度

/**
 * @brief  初始化DAC通道1 (PA4)
 * @param  None
 * @retval None
 * @note   PA4配置为DAC_OUT1，12位分辨率
 */
void DAC_PA4_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能GPIOA和DAC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    
    // 配置PA4为模拟模式
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;        // 模拟模式
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;   // 无上下拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置DAC通道1
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_T6_TRGO;                 // TIM6触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;      // 无波形生成
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;        // 使能输出缓冲器
    
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);
    
    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);
    
    // 设置初始输出值为1.65V (中间值)
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  初始化DAC的DMA配置
 * @param  None
 * @retval None
 * @note   配置DMA1 Stream5 Channel7用于DAC1输出
 */
void DAC_DMA_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能DMA1时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);

    // 复位DMA1 Stream5
    DMA_DeInit(DMA1_Stream5);
    while (DMA_GetCmdStatus(DMA1_Stream5) != DISABLE) {}

    // 配置DMA1 Stream5
    DMA_InitStructure.DMA_Channel = DMA_Channel_7;                          // DAC1使用Channel7
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(DAC->DHR12R1);   // DAC1数据寄存器地址
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)dma_buffer;           // DMA缓冲区地址
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;                 // 内存到外设
    DMA_InitStructure.DMA_BufferSize = DAC_SINE_SAMPLES;                    // 缓冲区大小
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;        // 外设地址不递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 // 内存地址递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord; // 16位数据
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;     // 16位数据
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;                         // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;                     // 高优先级
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;                  // 禁用FIFO
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;             // 单次传输
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;     // 单次传输

    DMA_Init(DMA1_Stream5, &DMA_InitStructure);

    // 配置DMA中断（可选，用于调试）
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Stream5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x03;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x02;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 使能DMA传输完成中断（可选）
    DMA_ITConfig(DMA1_Stream5, DMA_IT_TC, ENABLE);
}

/**
 * @brief  生成正弦波查找表
 * @param  amplitude: 正弦波幅度
 * @retval None
 * @note   根据指定幅度生成正弦波查找表
 */
static void DAC_GenerateSineTable(float amplitude)
{
    uint32_t i;

    // 生成正弦波查找表
    for (i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        float angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float sine_value = sinf(angle);

        // 将正弦波从[-1,1]映射到指定范围
        // 公式: voltage = 1.65V + amplitude * sin(angle)
        float voltage = DAC_OFFSET_VOLTAGE + (sine_value * amplitude);

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }
}

/**
 * @brief  初始化DAC正弦波输出
 * @param  None
 * @retval None
 * @note   生成正弦波查找表，配置DMA
 */
void DAC_SineWave_Init(void)
{
    // 初始化DMA
    DAC_DMA_Init();

    // 获取初始频率对应的幅度
    current_amplitude = DAC_GetAmplitudeForFrequency(dac_output_frequency);

    // 生成初始正弦波查找表
    DAC_GenerateSineTable(current_amplitude);

    // 复制查找表到DMA缓冲区
    DAC_UpdateDMABuffer();
}

/**
 * @brief  更新DMA缓冲区
 * @param  None
 * @retval None
 * @note   根据频率智能调整DMA缓冲区大小和内容
 */
static void DAC_UpdateDMABuffer(void)
{
    uint32_t i;

    // 根据频率决定DMA缓冲区的有效大小，目标是保持采样率在800kHz以下
    if (dac_output_frequency <= 3000.0f) {
        // 低频：使用完整的256点，最佳波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES;
        for (i = 0; i < DAC_SINE_SAMPLES; i++) {
            dma_buffer[i] = sine_table[i];
        }
    } else if (dac_output_frequency <= 6000.0f) {
        // 中频：使用128点，良好波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 2;
        for (i = 0; i < current_dma_buffer_size; i++) {
            // 从256点表中均匀采样128点
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    } else if (dac_output_frequency <= 12000.0f) {
        // 高频：使用64点，可接受波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 4;
        for (i = 0; i < current_dma_buffer_size; i++) {
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    } else {
        // 超高频：使用32点，基本波形质量 (最高3.2MHz采样率，但32点足够)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 8;
        for (i = 0; i < current_dma_buffer_size; i++) {
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    }
}

/**
 * @brief  根据频率获取对应的幅度
 * @param  frequency: 输入频率 (Hz)
 * @retval 对应的幅度值
 * @note   使用线性插值计算中间频率的幅度
 */
float DAC_GetAmplitudeForFrequency(float frequency)
{
    // 频率超过100kHz时返回0
    if (frequency > DAC_MAX_FREQ_HZ)
        return 0.0f;

    // 频率低于100Hz时使用100Hz的幅度
    if (frequency < DAC_BASE_FREQ)
        frequency = DAC_BASE_FREQ;

    // 计算在表中的位置
    float table_index = (frequency - DAC_BASE_FREQ) / DAC_FREQ_STEP;

    // 如果超出表范围，使用1V峰峰值
    if (table_index >= DAC_FREQ_TABLE_SIZE)
        return 0.5f;  // 1V峰峰值对应0.5V幅度

    // 获取整数部分和小数部分
    uint32_t index = (uint32_t)table_index;
    float fraction = table_index - index;

    // 线性插值
    float amplitude_factor;
    if (index >= DAC_FREQ_TABLE_SIZE - 1)
    {
        amplitude_factor = amplitude_table[DAC_FREQ_TABLE_SIZE - 1];
    }
    else
    {
        amplitude_factor = amplitude_table[index] +
                          fraction * (amplitude_table[index + 1] - amplitude_table[index]);
    }

    // 计算实际幅度：DAC峰峰值 = (1V / amplitude_factor) * multiplier
    // 这样经过外部电路放大后：DAC峰峰值 * amplitude_factor = 1V * multiplier
    float peak_to_peak = (1.0f / amplitude_factor) * dac_amplitude_multiplier;
    float amplitude = peak_to_peak / 2.0f;

    // 限制幅度不超过1.65V (确保电压在0-3.3V范围内)
    if (amplitude > DAC_OFFSET_VOLTAGE)
        amplitude = DAC_OFFSET_VOLTAGE;

    return amplitude;
}

/**
 * @brief  设置DAC通道1的数字值
 * @param  value: 12位数字值 (0-4095)
 * @retval None
 * @note   输出电压 = (value / 4095) * VREF+
 */
void DAC_SetChannel1Value(uint16_t value)
{
    // 限制值在12位范围内
    if (value > 4095)
        value = 4095;
    
    DAC_SetChannel1Data(DAC_Align_12b_R, value);
}

/**
 * @brief  设置DAC通道1的输出电压
 * @param  voltage: 输出电压值 (0.0V - 3.3V)
 * @retval None
 * @note   假设VREF+ = 3.3V
 */
void DAC_SetChannel1Voltage(float voltage)
{
    uint16_t dac_value;
    
    // 限制电压范围
    if (voltage < 0.0f)
        voltage = 0.0f;
    if (voltage > 3.3f)
        voltage = 3.3f;
    
    // 计算对应的DAC值
    dac_value = (uint16_t)((voltage / 3.3f) * 4095.0f);
    
    DAC_SetChannel1Value(dac_value);
}

/**
 * @brief  设置DAC正弦波输出频率
 * @param  frequency: 输出频率 (Hz)
 * @retval None
 * @note   当频率超过100kHz时，停止输出；频率改变时重新生成查找表
 */
void DAC_SetSineFrequency(float frequency)
{
    dac_output_frequency = frequency;

    // 检查频率限制
    if (frequency > DAC_MAX_FREQ_HZ)
    {
        DAC_StopSineOutput();
        return;
    }

    // 获取新频率对应的幅度
    float new_amplitude = DAC_GetAmplitudeForFrequency(frequency);

    // 如果幅度发生变化，重新生成查找表和DMA缓冲区
    if (new_amplitude != current_amplitude)
    {
        current_amplitude = new_amplitude;
        DAC_GenerateSineTable(current_amplitude);
        DAC_UpdateDMABuffer();
    }

    // 计算所需的定时器频率
    // 根据当前DMA缓冲区大小计算采样率
    float required_sample_rate = frequency * current_dma_buffer_size;

    // 更新TIM6的频率以匹配所需的采样率
    DAC_UpdateTimerFrequency(required_sample_rate);

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && frequency > 0 && frequency <= DAC_MAX_FREQ_HZ)
    {
        DAC_StartSineOutput();
    }
    else
    {
        DAC_StopSineOutput();
    }
}

/**
 * @brief  更新定时器频率
 * @param  sample_rate: 所需的采样率 (Hz)
 * @retval None
 * @note   动态调整TIM6的频率以匹配所需的采样率
 */
static void DAC_UpdateTimerFrequency(float sample_rate)
{
    printf("DAC_UpdateTimerFrequency: Setting rate to %.0f Hz\r\n", sample_rate);

    // 确保TIM6已经初始化
    static uint8_t tim6_initialized = 0;
    if (!tim6_initialized) {
        printf("DAC: Initializing TIM6 for first use\r\n");
        TIM6_DAC_Init(105 - 1, 1 - 1);  // 初始化TIM6
        tim6_initialized = 1;
    }

    // 限制采样率在合理范围内 - 回到保守设置
    if (sample_rate < 1000.0f) sample_rate = 1000.0f;        // 最小1kHz
    if (sample_rate > 2000000.0f) sample_rate = 2000000.0f;   // 最大2MHz，确保稳定性

    // 计算定时器参数
    // TIM6时钟频率：84MHz
    uint32_t timer_freq = 84000000;
    uint32_t period = (uint32_t)(timer_freq / sample_rate);

    // 确保period在有效范围内 (最小值为2，最大值为65535)
    if (period < 2) period = 2;
    if (period > 65535) period = 65535;

    printf("DAC Timer: period=%d, actual_freq=%d\r\n", period, timer_freq/period);

    // 停止定时器
    TIM_Cmd(TIM6, DISABLE);

    // 等待定时器停止（添加超时保护）
    uint32_t timeout = 10000;
    while(TIM_GetFlagStatus(TIM6, TIM_FLAG_Update) == SET && timeout > 0)
    {
        TIM_ClearFlag(TIM6, TIM_FLAG_Update);
        timeout--;
    }

    if (timeout == 0) {
        printf("DAC Timer: Warning - timeout waiting for TIM6 stop\r\n");
    }

    // 更新定时器周期
    TIM6->ARR = period - 1;

    // 重置计数器
    TIM6->CNT = 0;

    // 重新启动定时器
    TIM_Cmd(TIM6, ENABLE);
    printf("DAC Timer: TIM6 restarted\r\n");

    // 调试信息：打印实际配置的参数
    #ifdef DEBUG_DAC_TIMER
    printf("DAC Timer: freq=%.0f, period=%d, actual_freq=%d\r\n",
           sample_rate, period, timer_freq/period);
    #endif
}

/**
 * @brief  启动DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StartSineOutput(void)
{
    if (dac_user_enabled && dac_output_frequency <= DAC_MAX_FREQ_HZ)
    {
        dac_output_enabled = 1;

        // 停止DMA
        DMA_Cmd(DMA1_Stream5, DISABLE);

        // 重新设置DMA缓冲区大小
        DMA1_Stream5->NDTR = current_dma_buffer_size;

        // 启动DMA
        DMA_Cmd(DMA1_Stream5, ENABLE);

        // 使能DAC的DMA请求
        DAC_DMACmd(DAC_Channel_1, ENABLE);
    }
}

/**
 * @brief  停止DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StopSineOutput(void)
{
    dac_output_enabled = 0;

    // 禁用DAC的DMA请求
    DAC_DMACmd(DAC_Channel_1, DISABLE);

    // 停止DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);

    // 输出中间电压1.65V
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  更新DAC正弦波输出 (已改为DMA模式，不再使用此函数)
 * @param  None
 * @retval None
 * @note   DMA模式下，硬件自动从缓冲区读取数据输出
 */
/*
void DAC_UpdateSineOutput(void)
{
    if (!dac_output_enabled)
        return;

    // 更新相位
    current_phase += phase_increment;

    // 相位回绕
    if (current_phase >= DAC_SINE_SAMPLES)
    {
        current_phase -= DAC_SINE_SAMPLES;
    }

    // 获取当前索引
    sine_index = (uint32_t)current_phase;

    // 输出正弦波值
    DAC_SetChannel1Value(sine_table[sine_index]);
}
*/

/**
 * @brief  设置DAC幅度倍数
 * @param  multiplier: 幅度倍数 (1.0-2.0)
 * @retval None
 */
void DAC_SetAmplitudeMultiplier(float multiplier)
{
//    // 限制倍数范围在1.0-2.0之间
//    if (multiplier < 1.0f)
//        multiplier = 1.0f;
//    if (multiplier > 2.0f)
//        multiplier = 2.0f;

    dac_amplitude_multiplier = multiplier;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  切换到下一个幅度倍数
 * @param  None
 * @retval None
 * @note   循环：1.0 -> 1.1 -> 1.2 -> ... -> 2.0 -> 1.0
 */
void DAC_NextAmplitudeMultiplier(void)
{
    // 增加0.1倍数
    dac_amplitude_multiplier += 0.1f;

    // 如果超过2.0，回到1.0
    if (dac_amplitude_multiplier > 2.05f)
        dac_amplitude_multiplier = 1.0f;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  获取当前DAC幅度倍数
 * @param  None
 * @retval 当前幅度倍数
 */
float DAC_GetAmplitudeMultiplier(void)
{
    return dac_amplitude_multiplier;
}

/**
 * @brief  设置DAC用户使能状态
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void DAC_SetUserEnable(uint8_t enable)
{
    dac_user_enabled = enable;

    if (enable)
    {
        // 用户使能时，重新设置当前频率以确保正确的幅度和相位增量
        DAC_SetSineFrequency(dac_output_frequency);
    }
    else
    {
        // 用户禁用时，停止输出
        DAC_StopSineOutput();
    }
}

/**
 * @brief  获取DAC用户使能状态
 * @param  None
 * @retval 用户使能状态
 */
uint8_t DAC_GetUserEnable(void)
{
    return dac_user_enabled;
}

/**
 * @brief  使能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Enable(void)
{
    DAC_Cmd(DAC_Channel_1, ENABLE);
}

/**
 * @brief  失能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Disable(void)
{
    DAC_Cmd(DAC_Channel_1, DISABLE);
}

/**
 * @brief  DAC测试函数 - 输出不同电压值进行测试
 * @param  None
 * @retval None
 * @note   可以用万用表测量PA4引脚的电压变化
 */
void DAC_Test(void)
{
    // 测试不同的电压输出
    DAC_SetChannel1Voltage(0.0f);   // 0V
    delay_ms(1000);

    DAC_SetChannel1Voltage(1.65f);  // 1.65V (中间值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(3.3f);   // 3.3V (最大值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(0.8f);   // 0.8V
    delay_ms(1000);

    DAC_SetChannel1Voltage(2.5f);   // 2.5V
    delay_ms(1000);

    // 回到中间值
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  DMA1 Stream5中断处理函数
 * @param  None
 * @retval None
 * @note   用于调试DMA传输状态（可选）
 */
void DMA1_Stream5_IRQHandler(void)
{
    if (DMA_GetITStatus(DMA1_Stream5, DMA_IT_TCIF5) == SET)
    {
        // DMA传输完成中断
        // 在循环模式下，这个中断表示一个完整的正弦波周期已经输出完成

        // 清除中断标志位
        DMA_ClearITPendingBit(DMA1_Stream5, DMA_IT_TCIF5);
    }
}

/**
 * @brief  启动重构信号的DAC输出
 * @param  sample_rate: 输出采样率 (Hz)
 * @retval None
 * @note   使用定时器触发DAC输出重构信号
 */
// 外部变量声明（来自main.c）
extern uint16_t adc3_reconstructed[512];  // 保持512点用于DAC输出

void DAC_StartReconstructedOutput(float sample_rate)
{


    // 停止当前DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);
    DAC_DMACmd(DAC_Channel_1, DISABLE);

    // 配置定时器频率为指定采样率
    DAC_UpdateTimerFrequency(sample_rate);

    // 重新配置DMA为重构信号缓冲区
    DMA_InitTypeDef DMA_InitStructure;

    // 停止DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);

    // 等待DMA停止
    while(DMA_GetCmdStatus(DMA1_Stream5) != DISABLE);

    // 重新配置DMA
    DMA_DeInit(DMA1_Stream5);

    DMA_InitStructure.DMA_Channel = DMA_Channel_7;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&DAC->DHR12R1;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)adc3_reconstructed;  // 使用重构信号缓冲区
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = 512;  // 512个重构采样点
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;

    DMA_Init(DMA1_Stream5, &DMA_InitStructure);

    // 启动DMA
    DMA_Cmd(DMA1_Stream5, ENABLE);

    // 使能DAC的DMA请求
    DAC_DMACmd(DAC_Channel_1, ENABLE);

    // 启用定时器和DAC
    TIM_Cmd(TIM6, ENABLE);
    DAC_Cmd(DAC_Channel_1, ENABLE);


}

/**
 * @brief  停止重构信号的DAC输出
 * @param  None
 * @retval None
 */
void DAC_StartOptimizedReconstructedOutput(float sample_rate, uint16_t* data_buffer, uint16_t buffer_size)
{
    printf("DAC_StartOptimizedReconstructedOutput: rate=%.0f, size=%d\r\n", sample_rate, buffer_size);

    // 安全检查：确保缓冲区大小合理
    if (buffer_size < 8 || buffer_size > 512) {
        printf("DAC Error: Invalid buffer size %d\r\n", buffer_size);
        return;
    }

    // 安全检查：确保采样率合理 - 设置为2MHz上限
    if (sample_rate < 1000.0f || sample_rate > 2000000.0f) {
        printf("DAC Error: Invalid sample rate %.0f\r\n", sample_rate);
        return;
    }

    // 停止当前DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);
    DAC_DMACmd(DAC_Channel_1, DISABLE);

    // 注意：不再重新配置TIM6频率，因为TIM6已经由ADC3设置为同步频率
    // DAC_UpdateTimerFrequency(sample_rate);  // 注释掉，保持与ADC3同步
    printf("DAC: Using existing TIM6 configuration for synchronization with ADC3\r\n");

    // 重新配置DMA为优化的缓冲区
    DMA_InitTypeDef DMA_InitStructure;

    // 停止DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);

    // 等待DMA停止
    while(DMA_GetCmdStatus(DMA1_Stream5) != DISABLE);

    // 重新配置DMA
    DMA_DeInit(DMA1_Stream5);

    DMA_InitStructure.DMA_Channel = DMA_Channel_7;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&DAC->DHR12R1;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)data_buffer;  // 使用优化的缓冲区
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = buffer_size;  // 使用优化的缓冲区大小
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;

    DMA_Init(DMA1_Stream5, &DMA_InitStructure);
    printf("DAC: DMA configured\r\n");

    // 启动DMA
    DMA_Cmd(DMA1_Stream5, ENABLE);
    printf("DAC: DMA enabled\r\n");

    // 使能DAC的DMA请求
    DAC_DMACmd(DAC_Channel_1, ENABLE);
    printf("DAC: DAC DMA enabled\r\n");

    // 启用定时器和DAC
    TIM_Cmd(TIM6, ENABLE);
    DAC_Cmd(DAC_Channel_1, ENABLE);
    printf("DAC: TIM6 and DAC enabled\r\n");

    printf("DAC_StartOptimizedReconstructedOutput: Completed successfully\r\n");
}

void DAC_StopReconstructedOutput(void)
{
    // 停止DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);
    DAC_DMACmd(DAC_Channel_1, DISABLE);

    // 停止定时器
    TIM_Cmd(TIM6, DISABLE);

    // 禁用DAC
    DAC_Cmd(DAC_Channel_1, DISABLE);

    // 输出中间电压
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);


}



/**
 * @brief  生成多频率谐波合成信号
 * @param  harmonics: 谐波分量数组，包含频率、幅度和相位信息
 * @param  num_harmonics: 谐波数量 (最多10个)
 * @retval None
 * @note   直接叠加指定频率、幅度和相位的多个正弦波信号
 */
void DAC_GenerateMultiFrequencySignal(HarmonicComponent_t* harmonics, int num_harmonics)
{
    uint32_t i, harmonic;
    float max_amplitude = 0.0f;

    // 调试信息
    printf("DAC_GenerateMultiFrequencySignal: num_harmonics=%d\r\n", num_harmonics);

    // 限制谐波数量
    if (num_harmonics > 10) num_harmonics = 10;
    if (num_harmonics < 1) num_harmonics = 1;

    // 找到最大幅度用于归一化，防止削波
    for (harmonic = 0; harmonic < num_harmonics; harmonic++) {
        if (harmonics[harmonic].amplitude > max_amplitude) {
            max_amplitude = harmonics[harmonic].amplitude;
        }
    }

    // 避免除零
    if (max_amplitude <= 0.0f) {
        max_amplitude = 0.1f;  // 默认100mV
    }

    // 计算缩放因子以防止削波
    float max_dac_amplitude = DAC_OFFSET_VOLTAGE * 0.9f;  // 使用90%的可用范围
    float scale_factor = 1.0f;
    if (max_amplitude > max_dac_amplitude) {
        scale_factor = max_dac_amplitude / max_amplitude;
    }

    // 找到基频（通常是第一个谐波）作为参考频率
    float fundamental_freq = 1000.0f;  // 默认1kHz
    if (num_harmonics > 0 && harmonics[0].frequency > 0.0f) {
        fundamental_freq = harmonics[0].frequency;
    }

    // 改进的多频率合成算法
    // 使用基频周期作为查找表的基准，确保周期性和稳定性

    for (i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        // 使用归一化角度，确保基频在查找表中正好完成一个周期
        float base_angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float composite_value = 0.0f;

        // 叠加所有谐波分量
        for (harmonic = 0; harmonic < num_harmonics; harmonic++) {
            if (harmonics[harmonic].amplitude > 0.0f && harmonics[harmonic].frequency > 0.0f) {
                // 计算该谐波相对于基频的倍数关系
                float harmonic_ratio = harmonics[harmonic].frequency / fundamental_freq;

                // 计算该谐波的相位（基于基频的整数倍关系）
                float harmonic_angle = base_angle * harmonic_ratio + harmonics[harmonic].phase;

                // 计算该谐波的贡献
                float harmonic_contribution = harmonics[harmonic].amplitude * sinf(harmonic_angle) * scale_factor;
                composite_value += harmonic_contribution;

                // 调试信息（只打印前几个点）
                if (i < 3) {
                    printf("Sample %d: H%d ratio=%.2f, angle=%.3f, contrib=%.6f\r\n",
                           i, harmonic+1, harmonic_ratio, harmonic_angle, harmonic_contribution);
                }
            }
        }

        // 添加直流偏移
        float voltage = DAC_OFFSET_VOLTAGE + composite_value;

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }

    // 设置输出频率为基频
    dac_output_frequency = fundamental_freq;

    // 更新DMA缓冲区
    DAC_UpdateDMABuffer();

    // 改进的采样率计算 - 固定使用256点每周期
    float required_sample_rate = fundamental_freq * 256;

    // 验证采样率是否满足所有谐波分量的要求
    float highest_freq = fundamental_freq;
    for (int h = 0; h < num_harmonics; h++) {
        if (harmonics[h].frequency > highest_freq) {
            highest_freq = harmonics[h].frequency;
        }
    }

    printf("Multi-freq: fundamental=%.0f Hz, highest=%.0f Hz, sample_rate=%.0f Hz\r\n",
           fundamental_freq, highest_freq, required_sample_rate);

    // 更新TIM6的频率以匹配所需的采样率
    DAC_UpdateTimerFrequency(required_sample_rate);

    // 调试信息
    printf("DAC: user_enabled=%d, freq=%.0f, max_freq=%.0f\r\n",
           dac_user_enabled, fundamental_freq, (float)DAC_MAX_FREQ_HZ);

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && fundamental_freq > 0 && fundamental_freq <= DAC_MAX_FREQ_HZ)
    {
        printf("DAC: Starting sine output...\r\n");
        DAC_StartSineOutput();
    }
    else
    {
        printf("DAC: Not starting - conditions not met\r\n");
    }
}

/**
 * @brief  生成单频率正弦信号
 * @param  frequency: 输出频率 (Hz)
 * @param  amplitude: 输出幅度 (V)
 * @retval None
 * @note   生成稳定的单频率正弦波信号
 */
void DAC_GenerateSingleFrequencySignal(float frequency, float amplitude)
{
    printf("DAC_GenerateSingleFrequencySignal: freq=%.0f Hz, amp=%.3f V\r\n", frequency, amplitude);

    // 限制幅度范围
    if (amplitude > DAC_OFFSET_VOLTAGE * 0.9f) {
        amplitude = DAC_OFFSET_VOLTAGE * 0.9f;
    }
    if (amplitude < 0.01f) {
        amplitude = 0.01f;  // 最小10mV
    }

    // 生成单频率正弦波查找表
    for (uint32_t i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        float angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float sine_value = amplitude * sinf(angle);

        // 添加直流偏移
        float voltage = DAC_OFFSET_VOLTAGE + sine_value;

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }

    // 设置输出频率
    dac_output_frequency = frequency;

    // 更新DMA缓冲区
    DAC_UpdateDMABuffer();

    // 计算所需的定时器频率（标准方法）
    float required_sample_rate = frequency * DAC_SINE_SAMPLES;

    // 更新TIM6的频率
    DAC_UpdateTimerFrequency(required_sample_rate);

    printf("DAC Single Freq: sample_rate=%.0f Hz\r\n", required_sample_rate);

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && frequency > 0 && frequency <= DAC_MAX_FREQ_HZ)
    {
        printf("DAC: Starting single frequency output...\r\n");
        DAC_StartSineOutput();
    }
    else
    {
        printf("DAC: Not starting single freq - conditions not met\r\n");
    }
}

/**
 * @brief  停止谐波合成输出
 * @param  None
 * @retval None
 * @note   停止DAC谐波输出并恢复到中间电压
 */
void DAC_StopHarmonicOutput(void)
{
    DAC_StopSineOutput();
}

/**
 * @brief  DAC简单测试函数 - 输出固定电压
 * @param  None
 * @retval None
 * @note   用于验证DAC基本功能
 */
void DAC_SimpleTest(void)
{
    // 输出2.0V测试电压
    DAC_SetChannel1Voltage(2.0f);
}
