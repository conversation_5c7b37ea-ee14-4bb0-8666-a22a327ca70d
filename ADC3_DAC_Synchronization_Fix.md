# ADC3-DAC同步修复方案

## 问题描述
ADC3输入信号与DAC输出信号在示波器上漂移严重，主要原因：
1. ADC3采样率与DAC输出率不同步
2. 相位不一致
3. 频率精度问题
4. 时钟源不同步

## 解决方案

### 1. 修改智能重构逻辑
- **文件**: `USER/main.c`
- **函数**: `ADC3_IntelligentReconstruction()`
- **修改内容**:
  - 添加基于10kHz阈值的判断逻辑
  - 基频≥10kHz：输出标准正弦波（相位置零，峰峰值2V）
  - 基频<10kHz：执行FFT-IFFT-DAC输出

### 2. 新增2V峰峰值标准正弦波重构函数
- **函数**: `ADC3_ReconstructStandardSineWave_2VPP()`
- **特点**:
  - 相位置零（从0度开始）
  - 峰峰值固定2V（1V幅度）
  - 频率与检测到的基波频率一致
  - **关键改进**: 与ADC3采样率同步

### 3. 同步采样率算法
```c
const float ADC3_ACTUAL_SAMPLE_RATE = 102314.0f;  // ADC3实际采样率

// 计算DAC采样率与ADC3采样率的比率
float ratio = ADC3_ACTUAL_SAMPLE_RATE / dac_sample_rate;
float rounded_ratio = roundf(ratio);

// 如果比率接近整数，调整以获得精确的整数倍关系
if (fabs(ratio - rounded_ratio) < 0.05f && rounded_ratio >= 1.0f) {
    dac_sample_rate = ADC3_ACTUAL_SAMPLE_RATE / rounded_ratio;
    points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
}
```

### 4. 修改复杂波形重构函数
- **函数**: `ADC3_ReconstructComplexWaveform()`
- **修改内容**:
  - 使用相同的同步采样率计算逻辑
  - 确保复杂波形也与ADC3同步

## 技术细节

### ADC3配置
- **采样率**: 102.314kHz (84MHz / 821 / 1)
- **触发源**: TIM5
- **采样点数**: 512点
- **频率分辨率**: 102314/512 ≈ 199.8Hz

### DAC同步策略
1. **整数倍关系**: 确保DAC采样率是ADC3采样率的整数分之一
2. **相位对齐**: 标准正弦波从0相位开始
3. **频率精确**: 使用ADC3实际采样率计算精确频率

### 同步性能评估
```c
float final_ratio = ADC3_ACTUAL_SAMPLE_RATE / dac_sample_rate;
float sync_error = fabs(final_ratio - roundf(final_ratio));

if (sync_error < 0.01f) {
    printf("Synchronization: EXCELLENT (ratio ≈ %.0f:1)\r\n", roundf(final_ratio));
} else if (sync_error < 0.05f) {
    printf("Synchronization: GOOD\r\n");
} else {
    printf("Synchronization: FAIR (may cause drift)\r\n");
}
```

## 预期效果

### 高频信号（≥10kHz）
- 输出标准正弦波，相位置零
- 峰峰值固定2V
- 频率与基波频率精确匹配
- 与ADC3采样率同步，消除漂移

### 低频信号（<10kHz）
- 保持原有的FFT-IFFT重构逻辑
- 但使用同步的DAC采样率
- 减少漂移现象

## 使用方法

1. **编译代码**: 确保所有修改都已正确编译
2. **连接信号**: 将测试信号连接到ADC3输入（PA5）
3. **启动处理**: 按下"PROC ON"按钮启动ADC3处理
4. **观察输出**: 在示波器上同时观察ADC3输入和DAC输出
5. **验证同步**: 检查串口输出的同步性能报告

## 调试信息

修改后的代码会输出详细的同步信息：
```
=== ADC3 Standard Sine Wave Reconstruction (2V P-P, Zero Phase, Synchronized) ===
Target frequency: 15000.0 Hz
ADC3 actual sample rate: 102314.0 Hz
Initial DAC/ADC3 sample rate ratio: 4.263
Adjusted for integer ratio 4:1, new points per cycle: 7
=== Final DAC Configuration (Synchronized) ===
ADC3 sample rate: 102314.0 Hz
DAC sample rate: 25579 Hz
Synchronization: EXCELLENT (ratio ≈ 4:1)
```

## 注意事项

1. **频率范围**: 确保输入信号频率在ADC3的有效检测范围内
2. **幅度**: 输入信号幅度应适中，避免ADC饱和
3. **稳定性**: 等待几秒钟让系统稳定后再观察结果
4. **示波器设置**: 使用适当的时基和触发设置观察同步效果
