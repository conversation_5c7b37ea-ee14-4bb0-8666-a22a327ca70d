# ADC3-DAC同步解决方案

## 问题分析
ADC3输入信号与DAC输出信号在示波器上漂移的根本原因是：
- **ADC3使用TIM5触发** (102.314kHz)
- **DAC使用TIM6触发** (动态频率)
- **两个定时器独立运行**，没有同步机制

## 解决方案：统一时钟源

### 核心思路
让ADC3和DAC都使用**同一个定时器TIM6**作为触发源，确保完全同步。

### 修改内容

#### 1. ADC3配置修改 (`HARDWARE/ADC/adc.c`)
```c
// 原来：使用软件触发
ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;
ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T1_CC1;

// 修改后：使用TIM6触发，与DAC同步
ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T6_TRGO;
```

#### 2. 启动逻辑修改 (`USER/main.c`)
```c
// 原来：使用TIM5触发ADC3
TIM5_ADC3_Init(821 - 1, 1 - 1);
TIM_Cmd(TIM5, ENABLE);

// 修改后：使用TIM6同时触发ADC3和DAC
TIM6_DAC_Init(821 - 1, 1 - 1);  // 102.314kHz
TIM_Cmd(TIM6, ENABLE);
```

#### 3. DAC输出修改 (`HARDWARE/DAC/dac.c`)
```c
// 在DAC_StartOptimizedReconstructedOutput函数中
// 注释掉重新配置TIM6的代码，保持与ADC3同步
// DAC_UpdateTimerFrequency(sample_rate);  // 注释掉
printf("DAC: Using existing TIM6 configuration for synchronization with ADC3\r\n");
```

#### 4. 禁用TIM5中断 (`HARDWARE/TIMER2/timer.c`)
```c
// 注释掉整个TIM5_IRQHandler函数，因为不再使用TIM5
/*
void TIM5_IRQHandler(void)
{
    // 原来的中断处理代码...
}
*/
```

### 工作原理

1. **统一触发源**: ADC3和DAC都由TIM6的TRGO信号触发
2. **同步采样**: ADC3在每个TIM6周期采样一次
3. **同步输出**: DAC在每个TIM6周期输出一个数据点
4. **相位锁定**: 两个信号在时域上完全对齐

### 时序图
```
TIM6 TRGO:  ↑     ↑     ↑     ↑     ↑
            |     |     |     |     |
ADC3:       采样   采样   采样   采样   采样
            |     |     |     |     |
DAC:        输出   输出   输出   输出   输出
```

### 预期效果

1. **消除漂移**: ADC3输入和DAC输出信号在示波器上稳定显示
2. **相位对齐**: 两个信号的相位关系固定
3. **频率精确**: 输出频率与检测频率完全一致

### 使用方法

1. **编译代码**: 确保所有修改都正确编译
2. **连接信号**: 
   - ADC3输入: PA5引脚
   - DAC输出: PA4引脚
3. **示波器设置**:
   - 通道1: ADC3输入信号
   - 通道2: DAC输出信号
   - 触发: 使用其中一个通道作为触发源
4. **启动测试**:
   - 按下"PROC ON"按钮
   - 观察两个信号应该稳定显示，无漂移

### 调试信息

修改后的代码会输出同步相关信息：
```
DAC: Using existing TIM6 configuration for synchronization with ADC3
ADC3: Starting processing, samples=512
DAC_StartOptimizedReconstructedOutput: rate=102314, size=64
```

### 注意事项

1. **采样率固定**: ADC3采样率固定为102.314kHz
2. **DAC输出率**: DAC输出率必须是102.314kHz的整数分之一
3. **缓冲区大小**: 确保DAC缓冲区大小合理(8-512点)
4. **信号幅度**: 输入信号幅度适中，避免ADC饱和

### 故障排除

如果仍有漂移：
1. 检查TIM6是否正确初始化
2. 确认ADC3和DAC都使用TIM6触发
3. 验证DMA配置是否正确
4. 检查示波器触发设置

### 技术优势

- **硬件同步**: 使用硬件定时器确保精确同步
- **零漂移**: 消除软件延迟导致的漂移
- **实时性**: 硬件触发保证实时性能
- **稳定性**: 不受系统负载影响
